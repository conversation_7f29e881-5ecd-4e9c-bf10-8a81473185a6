21:28:56.166 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1005184 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
21:28:56.167 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
21:28:57.100 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
21:28:57.101 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:28:57.101 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
21:28:57.152 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:28:57.805 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
21:28:58.116 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
21:28:58.805 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
21:28:58.822 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.114 seconds (JVM running for 3.783)
21:29:19.006 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:33:36.328 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:33:36.328 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:33:36.526 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 3行 x 8列
21:33:36.611 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:33:36.611 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:33:36.641 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:33:36.643 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:33:36.656 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:33:36.724 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2881 bytes
21:33:36.730 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213336.docx, 大小: 2881 bytes
21:34:14.479 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:34:14.479 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:34:14.481 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:34:14.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:34:14.484 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:34:14.484 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:34:14.486 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:34:14.486 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:34:14.492 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:34:14.493 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:34:14.494 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:34:14.500 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2817 bytes
21:34:14.503 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213414.docx, 大小: 2817 bytes
21:36:58.003 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:36:58.004 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:36:58.006 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:36:58.007 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:36:58.011 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:36:58.012 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:36:58.017 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:36:58.018 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:36:58.019 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:36:58.027 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2817 bytes
21:36:58.034 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213658.docx, 大小: 2817 bytes
21:44:01.962 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:44:01.962 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:44:01.963 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:44:01.966 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:44:01.966 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
21:44:01.970 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:44:01.971 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:44:01.972 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:44:01.976 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2822 bytes
21:44:01.979 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_214401.docx, 大小: 2822 bytes
21:44:05.327 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:44:05.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
21:44:05.337 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:44:05.339 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:44:05.340 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:44:05.344 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2823 bytes
21:44:05.347 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_214405.docx, 大小: 2823 bytes
21:56:58.913 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
21:56:58.916 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:06:24.186 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1052998 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:06:24.188 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:06:25.244 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:06:25.245 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:06:25.245 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:06:25.298 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:06:25.932 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:06:26.231 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:06:27.028 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:06:27.047 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.387 seconds (JVM running for 4.164)
22:06:34.463 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:06:34.496 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,105] - 开始测试Word导出...
22:06:34.497 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 测试检验记录表
22:06:34.788 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 5行 x 8列
22:06:34.994 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:06:34.994 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
22:06:35.034 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:06:35.038 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:06:35.064 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:06:35.157 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 3104 bytes
22:06:35.173 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,122] - 测试导出成功，文件名: %E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250818_220635.docx, 大小: 3104 bytes
22:13:16.058 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
22:13:16.060 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:13:20.608 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1061865 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:13:20.609 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:13:21.651 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:13:21.652 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:13:21.652 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:13:21.705 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:13:22.324 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:13:22.607 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:13:23.350 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:13:23.369 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.204 seconds (JVM running for 3.897)
22:13:28.704 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:13:28.726 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,105] - 开始测试Word导出...
22:13:28.727 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 测试检验记录表
22:13:28.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,431] - 已设置文档为横向纸张
22:13:29.142 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 5行 x 8列
22:13:29.181 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:13:29.182 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
22:13:29.220 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:13:29.224 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:13:29.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:13:29.361 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 3099 bytes
22:13:29.382 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,122] - 测试导出成功，文件名: %E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250818_221329.docx, 大小: 3099 bytes
22:13:53.619 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [testEmptyDataExport,139] - 开始测试空数据Word导出...
22:13:53.619 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 空数据测试检验记录表
22:13:53.621 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,431] - 已设置文档为横向纸张
22:13:53.622 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:13:53.622 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
22:13:53.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:13:53.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:13:53.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:13:53.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
22:13:53.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:13:53.627 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
22:13:53.634 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:13:53.635 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:13:53.636 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:13:53.643 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2833 bytes
22:13:53.647 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [testEmptyDataExport,156] - 空数据测试导出成功，文件名: %E7%A9%BA%E6%95%B0%E6%8D%AE%E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250818_221353.docx, 大小: 2833 bytes
