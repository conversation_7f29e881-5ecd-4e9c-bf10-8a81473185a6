21:28:56.166 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1005184 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
21:28:56.167 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
21:28:57.100 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
21:28:57.101 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:28:57.101 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
21:28:57.152 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:28:57.805 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
21:28:58.116 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
21:28:58.805 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
21:28:58.822 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.114 seconds (JVM running for 3.783)
21:29:19.006 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:33:36.328 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:33:36.328 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:33:36.526 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 3行 x 8列
21:33:36.611 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:33:36.611 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:33:36.641 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:33:36.643 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:33:36.656 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:33:36.724 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2881 bytes
21:33:36.730 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213336.docx, 大小: 2881 bytes
21:34:14.479 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:34:14.479 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:34:14.481 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:34:14.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:34:14.484 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:34:14.484 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:34:14.486 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:34:14.486 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:34:14.492 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:34:14.493 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:34:14.494 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:34:14.500 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2817 bytes
21:34:14.503 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213414.docx, 大小: 2817 bytes
21:36:58.003 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:36:58.004 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:36:58.006 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:36:58.007 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:36:58.011 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:36:58.012 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:36:58.017 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:36:58.018 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:36:58.019 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:36:58.027 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2817 bytes
21:36:58.034 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213658.docx, 大小: 2817 bytes
21:44:01.962 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:44:01.962 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:44:01.963 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:44:01.966 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:44:01.966 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
21:44:01.970 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:44:01.971 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:44:01.972 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:44:01.976 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2822 bytes
21:44:01.979 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_214401.docx, 大小: 2822 bytes
21:44:05.327 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:44:05.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
21:44:05.337 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:44:05.339 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:44:05.340 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:44:05.344 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2823 bytes
21:44:05.347 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_214405.docx, 大小: 2823 bytes
21:56:58.913 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
21:56:58.916 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
