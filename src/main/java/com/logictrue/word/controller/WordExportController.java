package com.logictrue.word.controller;

import com.logictrue.word.dto.TableExportRequest;
import com.logictrue.word.service.WordExportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Word导出控制器
 */
@Slf4j
@RestController
@RequestMapping("/wordExport")
@RequiredArgsConstructor
public class WordExportController {
    
    private final WordExportService wordExportService;
    
    /**
     * 导出表格到Word文档
     */
    @PostMapping("/exportTable")
    public ResponseEntity<byte[]> exportTable(@RequestBody TableExportRequest request) {
        try {
            log.info("接收到表格导出请求，标题: {}", request.getTitle());
            
            // 导出Word文档
            byte[] wordBytes = wordExportService.exportTableToWord(request);
            
            // 生成文件名
            String fileName = generateFileName(request.getTitle());
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);
            
            log.info("表格导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);
            
            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);
            
        } catch (IOException e) {
            log.error("导出Word文档失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String title) {
        try {
            String baseFileName = (title != null && !title.trim().isEmpty()) 
                    ? title.trim() 
                    : "表格导出";
            
            // 添加时间戳
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = baseFileName + "_" + timestamp + ".docx";
            
            // URL编码文件名以支持中文
            return URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");
                    
        } catch (Exception e) {
            log.warn("生成文件名失败，使用默认文件名", e);
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            return "table_export_" + timestamp + ".docx";
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Word导出服务运行正常");
    }

    /**
     * GET方式测试导出接口 - 生成一个简单的测试Word文档
     */
    @GetMapping("/testExport")
    public ResponseEntity<byte[]> testExport() {
        try {
            log.info("开始测试Word导出...");

            // 创建测试数据
            TableExportRequest testRequest = createTestRequest();

            // 导出Word文档
            byte[] wordBytes = wordExportService.exportTableToWord(testRequest);

            // 生成文件名
            String fileName = generateFileName("测试导出");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("测试导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            log.error("测试导出失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("测试导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * GET方式测试空数据导出接口 - 测试数据行为空时的表头对齐问题
     */
    @GetMapping("/testEmptyDataExport")
    public ResponseEntity<byte[]> testEmptyDataExport() {
        try {
            log.info("开始测试空数据Word导出...");

            // 创建空数据测试数据
            TableExportRequest testRequest = createEmptyDataTestRequest();

            // 导出Word文档
            byte[] wordBytes = wordExportService.exportTableToWord(testRequest);

            // 生成文件名
            String fileName = generateFileName("空数据测试导出");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("空数据测试导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            log.error("空数据测试导出失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("空数据测试导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 创建测试请求数据
     */
    private TableExportRequest createTestRequest() {
        TableExportRequest request = new TableExportRequest();
        request.setTitle("测试检验记录表");
        request.setTableWidth(1200);
        request.setTableHeight(600);
        request.setPageOrientation("LANDSCAPE"); // 默认横向

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();

        // 创建表头
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();

        // 第一行表头 - 8列表格
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("检查工序名称", 2, 1, 120, 50));    // 列1
        headerRow1.add(createHeaderCell("检查项目及技术条件", 2, 1, 200, 50)); // 列2
        headerRow1.add(createHeaderCell("实际检查结果", 2, 1, 150, 50));     // 列3
        headerRow1.add(createHeaderCell("完工", 1, 2, 120, 25));           // 列4-5，colspan=2
        headerRow1.add(createHeaderCell("操作员", 2, 1, 100, 50));          // 列6
        headerRow1.add(createHeaderCell("班组长", 2, 1, 100, 50));          // 列7
        headerRow1.add(createHeaderCell("检验员", 2, 1, 100, 50));          // 列8
        headers.add(headerRow1);

        // 第二行表头 - 对应8列
        List<TableExportRequest.HeaderCell> headerRow2 = new ArrayList<>();
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列1占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列2占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列3占位符
        headerRow2.add(createHeaderCell("月", 1, 1, 60, 25));  // 列4
        headerRow2.add(createHeaderCell("日", 1, 1, 60, 25));  // 列5
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列6占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列7占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列8占位符
        headers.add(headerRow2);

        tableData.setHeaders(headers);

        // 创建数据行
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();

        // 测试数据行1
        List<TableExportRequest.DataCell> dataRow1 = new ArrayList<>();
        dataRow1.add(createDataCell("外观检查", false, 120, 50));
        dataRow1.add(createDataCell("表面无划痕、无变形", false, 200, 50));
        dataRow1.add(createDataCell("合格", false, 150, 50));
        dataRow1.add(createDataCell("12", false, 60, 50));
        dataRow1.add(createDataCell("25", false, 60, 50));
        dataRow1.add(createDataCell("张三", false, 100, 50));
        dataRow1.add(createDataCell("李四", false, 100, 50));
        dataRow1.add(createDataCell("王五", false, 100, 50));
        dataRows.add(dataRow1);

        // 测试数据行2 - 包含数学公式
        List<TableExportRequest.DataCell> dataRow2 = new ArrayList<>();
        dataRow2.add(createDataCell("尺寸检查", false, 120, 50));
        dataRow2.add(createDataCell("长度: $L = 100 \\pm 0.1$ mm", true, 200, 50));
        dataRow2.add(createDataCell("$L = 99.95$ mm", true, 150, 50));
        dataRow2.add(createDataCell("12", false, 60, 50));
        dataRow2.add(createDataCell("25", false, 60, 50));
        dataRow2.add(createDataCell("张三", false, 100, 50));
        dataRow2.add(createDataCell("李四", false, 100, 50));
        dataRow2.add(createDataCell("王五", false, 100, 50));
        dataRows.add(dataRow2);

        // 测试数据行3
        List<TableExportRequest.DataCell> dataRow3 = new ArrayList<>();
        dataRow3.add(createDataCell("功能测试", false, 120, 50));
        dataRow3.add(createDataCell("按规定程序操作", false, 200, 50));
        dataRow3.add(createDataCell("正常", false, 150, 50));
        dataRow3.add(createDataCell("12", false, 60, 50));
        dataRow3.add(createDataCell("26", false, 60, 50));
        dataRow3.add(createDataCell("赵六", false, 100, 50));
        dataRow3.add(createDataCell("钱七", false, 100, 50));
        dataRow3.add(createDataCell("孙八", false, 100, 50));
        dataRows.add(dataRow3);

        tableData.setDataRows(dataRows);
        request.setTableData(tableData);

        return request;
    }

    /**
     * 创建空数据测试请求 - 用于测试数据行为空时的表头对齐问题
     */
    private TableExportRequest createEmptyDataTestRequest() {
        TableExportRequest request = new TableExportRequest();
        request.setTitle("空数据测试检验记录表");
        request.setTableWidth(1200);
        request.setTableHeight(600);
        request.setPageOrientation("LANDSCAPE"); // 默认横向

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();

        // 创建表头
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();

        // 第一行表头 - 8列表格
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("检查工序名称", 2, 1, 120, 50));    // 列1
        headerRow1.add(createHeaderCell("检查项目及技术条件", 2, 1, 200, 50)); // 列2
        headerRow1.add(createHeaderCell("实际检查结果", 2, 1, 150, 50));     // 列3
        headerRow1.add(createHeaderCell("完工", 1, 2, 120, 25));           // 列4-5，colspan=2
        headerRow1.add(createHeaderCell("操作员", 2, 1, 100, 50));          // 列6
        headerRow1.add(createHeaderCell("班组长", 2, 1, 100, 50));          // 列7
        headerRow1.add(createHeaderCell("检验员", 2, 1, 100, 50));          // 列8
        headers.add(headerRow1);

        // 第二行表头 - 对应8列
        List<TableExportRequest.HeaderCell> headerRow2 = new ArrayList<>();
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列1占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列2占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列3占位符
        headerRow2.add(createHeaderCell("月", 1, 1, 60, 25));  // 列4
        headerRow2.add(createHeaderCell("日", 1, 1, 60, 25));  // 列5
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列6占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列7占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25));    // 列8占位符
        headers.add(headerRow2);

        tableData.setHeaders(headers);

        // 不设置数据行，模拟空数据情况
        tableData.setDataRows(new ArrayList<>());
        request.setTableData(tableData);

        return request;
    }

    /**
     * 创建表头单元格
     */
    private TableExportRequest.HeaderCell createHeaderCell(String content, Integer rowspan, Integer colspan, Integer width, Integer height) {
        TableExportRequest.HeaderCell cell = new TableExportRequest.HeaderCell();
        cell.setContent(content);
        cell.setRowspan(rowspan);
        cell.setColspan(colspan);
        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }

    /**
     * 创建数据单元格
     */
    private TableExportRequest.DataCell createDataCell(String content, Boolean hasMath, Integer width, Integer height) {
        TableExportRequest.DataCell cell = new TableExportRequest.DataCell();
        cell.setContent(content);
        cell.setHasMath(hasMath);
        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }
}
